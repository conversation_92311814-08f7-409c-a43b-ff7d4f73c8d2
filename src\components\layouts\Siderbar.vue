<template>
  <n-flex vertical class="h-100vh bg-#3075F6FF">
    <Logo></Logo>
    <n-layout-sider :native-scrollbar="false" width="338px">
      <n-scrollbar :style="{ maxHeight: 'calc(100vh - 190px)' }">
        <n-menu
          ref="menuRef"
          class="select-none"
          :value="activeKey"
          :options="siderbarStore.menuOptions"
          @update:value="handleMenuSelect"
        />
      </n-scrollbar>
    </n-layout-sider>
  </n-flex>
</template>

<script setup lang="ts">
import { NLayoutSider, NMenu, NFlex, NScrollbar } from 'naive-ui'
import Logo from '@/components/layouts/Logo.vue'
import { useSiderbar } from '@/hooks'
import { useSiderbarStore } from '@/stores'
import { onMounted, useTemplateRef } from 'vue'

const { activeKey, handleMenuSelect } = useSiderbar()

const siderbarStore = useSiderbarStore()
const menuRef = useTemplateRef<InstanceType<typeof NMenu>>('menuRef')

onMounted(() => {
  siderbarStore.menuRef = menuRef.value
})
</script>
