<template>
  <div class="flex flex-col">
    <!-- 表格容器 -->
    <div class="bg-white">
      <!-- 表头 -->
      <div
        class="grid px-2 rounded overflow-hidden text-16px leading-22px border border-solid border-#A9C0EA text-#6D84AE bg-#E4EBF8 select-none"
        :style="{ gridTemplateColumns: getGridTemplateColumns() }"
      >
        <div
          v-for="(column, index) in columns"
          :key="column.key"
          class="px-2 py-2.5 cursor-pointer flex items-center"
          :style="{
            justifyContent: column.align || 'center',
          }"
          @click="handleSort(column)"
        >
          <span>
            {{ column.title }}
          </span>
          <div v-if="column.sorter" class="flex flex-col ml-2">
            <span class="text-xs leading-none" :class="getSortIconClass(column, 'asc')"> ▲ </span>
            <span class="text-xs leading-none" :class="getSortIconClass(column, 'desc')"> ▼ </span>
          </div>
        </div>
      </div>

      <!-- 表格内容 -->
      <div class="relative">
        <n-virtual-list
          ref="virtualListRef"
          :key="virtualListKey"
          :style="{ maxHeight: tableHeight }"
          :item-size="itemSize"
          :items="tableData"
          class="mt-2"
        >
          <template #default="{ item, index }">
            <div
              :key="`${virtualListKey}-${index}`"
              class="grid px-2 py-2.5 mb-2.5 rounded bg-[#F8F8F8] hover:bg-#F4DBDB"
              :style="{
                gridTemplateColumns: getGridTemplateColumns(),
              }"
            >
              <div
                v-for="column in columns"
                :key="column.key"
                class="px-2 flex items-center text-20px"
                :style="{
                  color: '#5F6673',
                  justifyContent: column.align || 'center',
                }"
              >
                <slot
                  :name="column.key"
                  :item="item"
                  :value="getColumnValue(item, column.key)"
                  :index="index"
                >
                  {{ getColumnValue(item, column.key) }}
                </slot>
              </div>
            </div>
          </template>
        </n-virtual-list>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, computed, watch, nextTick } from 'vue'
import { NVirtualList } from 'naive-ui'

// 定义接口
interface TableColumn {
  key: string
  title: string
  width?: string
  align?: 'left' | 'center' | 'right'
  sorter?:
    | {
        compare?: (a: any, b: any) => number
        multiple?: number
      }
    | ((a: any, b: any) => number)
  defaultSortOrder?: 'asc' | 'desc' | false
}

interface SortState {
  key: string
  order: 'asc' | 'desc' | null
  multiple?: number
}

interface MultiSortState {
  [key: string]: {
    order: 'asc' | 'desc' | null
    multiple: number
  }
}

// 定义 props
interface Props {
  columns: TableColumn[]
  data: Record<string, any>[]
  height?: string
  itemSize?: number
}

const props = withDefaults(defineProps<Props>(), {
  height: '400px',
  itemSize: 62,
})

// 定义 emits
const emit = defineEmits<{
  sort: [column: TableColumn, order: 'asc' | 'desc' | null, multiSortState: MultiSortState]
}>()

// 响应式数据
const sortState = ref<SortState>({ key: '', order: null })
const multiSortState = ref<MultiSortState>({})
const virtualListRef = ref<InstanceType<typeof NVirtualList> | null>(null)
const virtualListKey = ref(0)
const tableData = ref<Record<string, any>[]>([])

// 监听数据变化，更新虚拟列表
watch(
  () => props.data,
  (newData) => {
    tableData.value = [...newData]
    // 强制重新渲染虚拟列表
    virtualListKey.value++
    nextTick(() => {
      virtualListRef.value?.scrollTo({ top: 0 })
    })
  },
  { immediate: true, deep: true },
)

// 计算属性
const tableHeight = computed(() => props.height)

// 方法
const getColumnValue = (item: Record<string, any>, key: string) => {
  return item[key] || ''
}

const getGridTemplateColumns = () => {
  return props.columns
    .map((column) => {
      if (column.width) {
        return column.width
      }
      return '1fr'
    })
    .join(' ')
}

const handleSort = (column: TableColumn) => {
  if (!column.sorter) return

  // 更新单列排序状态（保持向后兼容）
  if (sortState.value.key === column.key) {
    // 同一列：null -> asc -> desc -> null
    if (sortState.value.order === null) {
      sortState.value.order = 'asc'
    } else if (sortState.value.order === 'asc') {
      sortState.value.order = 'desc'
    } else {
      sortState.value.order = null
    }
  } else {
    // 不同列：重置为 asc
    sortState.value.key = column.key
    sortState.value.order = 'asc'
  }

  // 更新多列排序状态
  const currentOrder = sortState.value.order
  const multiple =
    column.sorter && typeof column.sorter === 'object' ? column.sorter.multiple || 1 : 1

  if (currentOrder === null) {
    // 移除该列的排序
    delete multiSortState.value[column.key]
  } else {
    // 添加或更新该列的排序
    multiSortState.value[column.key] = {
      order: currentOrder,
      multiple: multiple,
    }
  }

  emit('sort', column, sortState.value.order, multiSortState.value)
}

const getSortIconClass = (column: TableColumn, direction: 'asc' | 'desc') => {
  // 检查多列排序状态
  const multiSortColumn = multiSortState.value[column.key]
  if (multiSortColumn && multiSortColumn.order === direction) {
    return 'text-blue-600'
  }

  // 检查单列排序状态（向后兼容）
  if (sortState.value.key === column.key && sortState.value.order === direction) {
    return 'text-blue-600'
  }

  return 'text-gray-400'
}
</script>

<style scoped></style>
