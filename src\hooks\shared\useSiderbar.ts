import { useSiderbarStore } from '@/stores'
import { type MenuOption } from 'naive-ui'

import { onMounted, ref, watchEffect } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import { useStationDeviceList } from '../features/load-forecasting/useStationDeviceList'

export const useSiderbar = () => {
  const router = useRouter()
  const route = useRoute()
  const siderbarStore = useSiderbarStore()

  const activeKey = ref('section-monitoring')

  watchEffect(() => {
    const { query, name } = router.currentRoute.value
    activeKey.value = query.key ? (query.key as string) : (name as string)
    siderbarStore.menuRef?.showOption(activeKey.value)
  })

  /**
   * 如果item.route有值，则将key作为route的参数 ?key=
   * @param key
   * @param item
   */
  // TODO 动态获取当前地市下的设备后，无法展开地市
  const handleMenuSelect = (key: string, item: MenuOption) => {
    activeKey.value = key
    if (item.route) {
      const queryParams: Record<string, string> = {
        key,
      }

      if (item.regionCode) {
        queryParams['regionCode'] = item.regionCode as string
      }
      if (item.stationId) {
        queryParams['stationId'] = item.stationId as string
      }
      if (item.deviceId) {
        queryParams['deviceId'] = item.deviceId as string
      }
      if (item.label || item.name) {
        queryParams['name'] = (item.name as string) || (item.label as string)
      }
      if (item.type) {
        queryParams['type'] = item.type as string
      }
      // 保留时间范围
      if (item.route === '/load-forecasting/busbar-load' && route.query.timeRange) {
        queryParams['timeRange'] = route.query.timeRange as string
      }
      router.push({
        path: item.route as string,
        query: queryParams,
      })
    } else {
      router.push(`/${key}`)
    }
  }

  const { fetchStationDeviceList } = useStationDeviceList()

  onMounted(() => {
    const timeRange = route.query.timeRange

    // 默认获取当天数据
    fetchStationDeviceList(
      timeRange ? (JSON.parse(timeRange as string) as [number, number]) : undefined,
    )
  })
  return {
    activeKey,
    handleMenuSelect,
  }
}
