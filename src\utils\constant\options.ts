import type { MenuOption } from 'naive-ui'
import { renderIcon, renderText } from '../tools'
import icons from './icons'

export const cityRawOptions: MenuOption[] = [
  {
    label: renderText('全省', 'thirdLevel'),
    key: 'province',
    region: 'province',
    type: 'province',
    name: '全省',
    route: '/load-forecasting/busbar-load',
  },
  {
    label: renderText('南京', 'thirdLevel'),
    key: 'all-nanjing',
    regionCode: '320100',
    children: [
      {
        label: '南京',
        key: 'nanjing',
        region: '南京',
        regionCode: '320100',
        type: 'city',
        route: '/load-forecasting/busbar-load',
      },
    ],
  },
  {
    label: renderText('无锡', 'thirdLevel'),
    key: 'all-wuxi',
    regionCode: '320200',
    children: [
      {
        label: '无锡',
        key: 'wuxi',
        region: '无锡',
        regionCode: '320200',
        type: 'city',
        route: '/load-forecasting/busbar-load',
      },
    ],
  },
  {
    label: renderText('徐州', 'thirdLevel'),
    key: 'all-xuzhou',
    regionCode: '320300',
    children: [
      {
        label: '徐州',
        key: 'xuzhou',
        region: '徐州',
        regionCode: '320300',
        type: 'city',
        route: '/load-forecasting/busbar-load',
      },
    ],
  },
  {
    label: renderText('常州', 'thirdLevel'),
    key: 'all-changzhou',
    regionCode: '320400',
    children: [
      {
        label: '常州',
        key: 'changzhou',
        region: '常州',
        regionCode: '320400',
        type: 'city',
        route: '/load-forecasting/busbar-load',
      },
    ],
  },
  {
    label: renderText('苏州', 'thirdLevel'),
    key: 'all-suzhou',
    regionCode: '320500',
    children: [
      {
        label: '苏州',
        key: 'suzhou',
        region: '苏州',
        regionCode: '320500',
        type: 'city',
        route: '/load-forecasting/busbar-load',
      },
    ],
  },
  {
    label: renderText('南通', 'thirdLevel'),
    key: 'all-nantong',
    regionCode: '320600',
    children: [
      {
        label: '南通',
        key: 'nantong',
        region: '南通',
        regionCode: '320600',
        type: 'city',
        route: '/load-forecasting/busbar-load',
      },
    ],
  },
  {
    label: renderText('连云港', 'thirdLevel'),
    key: 'all-lianyungang',
    regionCode: '320700',
    children: [
      {
        label: '连云港',
        key: 'lianyungang',
        region: '连云港',
        regionCode: '320700',
        type: 'city',
        route: '/load-forecasting/busbar-load',
      },
    ],
  },
  {
    label: renderText('淮安', 'thirdLevel'),
    key: 'all-huaian',
    regionCode: '320800',
    children: [
      {
        label: '淮安',
        key: 'huaian',
        region: '淮安',
        regionCode: '320800',
        type: 'city',
        route: '/load-forecasting/busbar-load',
      },
    ],
  },
  {
    label: renderText('盐城', 'thirdLevel'),
    key: 'all-yancheng',
    regionCode: '320900',
    children: [
      {
        label: '盐城',
        key: 'yancheng',
        region: '盐城',
        regionCode: '320900',
        type: 'city',
        route: '/load-forecasting/busbar-load',
      },
    ],
  },
  {
    label: renderText('扬州', 'thirdLevel'),
    key: 'all-yangzhou',
    regionCode: '321000',
    children: [
      {
        label: '扬州',
        key: 'yangzhou',
        region: '扬州',
        regionCode: '321000',
        type: 'city',
        route: '/load-forecasting/busbar-load',
      },
    ],
  },
  {
    label: renderText('镇江', 'thirdLevel'),
    key: 'all-zhenjiang',
    regionCode: '321100',
    children: [
      {
        label: '镇江',
        key: 'zhenjiang',
        region: '镇江',
        regionCode: '321100',
        type: 'city',
        route: '/load-forecasting/busbar-load',
      },
    ],
  },
  {
    label: renderText('泰州', 'thirdLevel'),
    key: 'all-taizhou',
    regionCode: '321200',
    children: [
      {
        label: '泰州',
        key: 'taizhou',
        region: '泰州',
        regionCode: '321200',
        type: 'city',
        route: '/load-forecasting/busbar-load',
      },
    ],
  },
  {
    label: renderText('宿迁', 'thirdLevel'),
    key: 'all-suqian',
    regionCode: '321300',
    children: [
      {
        label: '宿迁',
        key: 'suqian',
        region: '宿迁',
        regionCode: '321300',
        type: 'city',
        route: '/load-forecasting/busbar-load',
      },
    ],
  },
]

export const menuRawOptions: MenuOption[] = [
  {
    label: renderText('断面监视', 'firstLevel'),
    key: 'section-monitoring',
    icon: renderIcon(icons.sectionMonitoringIcon),
  },
  {
    label: renderText('执行情况', 'firstLevel'),
    key: 'execution-situation',
    icon: renderIcon(icons.executionSituationIcon),
    children: [
      // {
      //   label: renderText('参与市场', 'secondLevel'),
      //   key: 'participate-market',
      // },
      // {
      //   label: renderText('煤炭', 'secondLevel'),
      //   key: 'coal',
      //   children: [
      //     {
      //       label: renderText('全省', 'thirdLevel'),
      //       key: 'all-province',
      //     },
      //     {
      //       label: renderText('江南', 'thirdLevel'),
      //       key: 'jiangnan',
      //     },
      //     {
      //       label: renderText('江北', 'thirdLevel'),
      //       key: 'jiangbei',
      //     },
      //   ],
      // },
      // {
      //   label: renderText('核电', 'secondLevel'),
      //   key: 'nuclear-power',
      // },
      // {
      //   label: renderText('风电', 'secondLevel'),
      //   key: 'wind-power',
      // },
      // {
      //   label: renderText('光伏', 'secondLevel'),
      //   key: 'photovoltaic',
      // },
      // {
      //   label: renderText('燃煤', 'secondLevel'),
      //   key: 'coal-fired',
      // },
      // {
      //   label: renderText('燃气', 'secondLevel'),
      //   key: 'gas',
      // },
      // {
      //   label: renderText('抽蓄', 'secondLevel'),
      //   key: 'pumped-storage',
      // },
      // {
      //   label: renderText('独立蓄能', 'secondLevel'),
      //   key: 'independent-storage',
      // },
      // {
      //   label: renderText('不参与市场', 'secondLevel'),
      //   key: 'not-participate-market',
      // },
      // {
      //   label: renderText('其他', 'secondLevel'),
      //   key: 'other',
      // },
      {
        label: renderText('新能源市场调峰日报', 'secondLevel'),
        key: 'daily-report',
      },
    ],
  },
  {
    label: renderText('负荷预测', 'firstLevel'),
    key: 'load-forecasting',
    icon: renderIcon(icons.loadForecastingIcon),
    children: [
      // {
      //   label: renderText('统调负荷', 'secondLevel'),
      //   key: 'unified-scheduling-load',
      // },
      {
        label: renderText('母线负荷', 'secondLevel'),
        key: 'busbar-load',
        children: [],
      },
    ],
  },
  {
    label: renderText('CPS及频率', 'firstLevel'),
    key: 'cps-frequency',
    icon: renderIcon(icons.cpsFrequencyIcon),
  },
]
